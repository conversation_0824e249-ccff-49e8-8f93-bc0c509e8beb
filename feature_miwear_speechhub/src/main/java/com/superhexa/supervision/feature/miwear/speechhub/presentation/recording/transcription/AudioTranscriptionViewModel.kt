@file:Suppress("<PERSON><PERSON><PERSON><PERSON>", "VariableNaming", "LargeClass", "MaxL<PERSON><PERSON>ength", "TooManyFunctions", "ForbiddenComment")

package com.superhexa.supervision.feature.miwear.speechhub.presentation.recording.transcription

import android.content.Context
import android.graphics.Bitmap
import android.webkit.WebView
import androidx.annotation.Keep
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.core.net.toUri
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import com.superhexa.music.utils.LiteJsonUtils.toJson
import com.superhexa.supervision.feature.miwear.speechhub.R
import com.superhexa.supervision.feature.miwear.speechhub.compont.getTemplateItemByValue
import com.superhexa.supervision.feature.miwear.speechhub.presentation.helper.ImageHelper
import com.superhexa.supervision.feature.miwear.speechhub.presentation.recording.PlayState
import com.superhexa.supervision.feature.miwear.speechhub.presentation.recording.constant.AudioTranscriptionShareType
import com.superhexa.supervision.feature.miwear.speechhub.presentation.recording.constant.ImageType
import com.superhexa.supervision.feature.miwear.speechhub.presentation.recording.constant.LoadingType
import com.superhexa.supervision.feature.miwear.speechhub.presentation.recording.helper.AudioPlayerController
import com.superhexa.supervision.feature.miwear.speechhub.service.RecordAudioService
import com.superhexa.supervision.feature.miwear.speechhub.service.TranscriptionNotificationManager
import com.superhexa.supervision.feature.miwear.speechhub.service.TranscriptionStateListener
import com.superhexa.supervision.feature.miwear.speechhub.utils.AudioTranscriptionManager
import com.superhexa.supervision.library.base.basecommon.config.ConstsConfig
import com.superhexa.supervision.library.base.basecommon.config.LibBaseApplication
import com.superhexa.supervision.library.base.basecommon.extension.DateTimeUtils
import com.superhexa.supervision.library.base.basecommon.extension.isNotNullOrEmpty
import com.superhexa.supervision.library.base.basecommon.extension.toast
import com.superhexa.supervision.library.base.basecommon.theme.Color129A37
import com.superhexa.supervision.library.base.basecommon.theme.Color1FA6AA
import com.superhexa.supervision.library.base.basecommon.theme.Color2971CF
import com.superhexa.supervision.library.base.basecommon.theme.ColorCB2E34
import com.superhexa.supervision.library.base.basecommon.theme.ColorD26913
import com.superhexa.supervision.library.base.basecommon.tools.InputUtil
import com.superhexa.supervision.library.base.basecommon.tools.MMKVUtils
import com.superhexa.supervision.library.base.presentation.mvi.BaseMVIViewModel
import com.superhexa.supervision.library.base.webviewhelper.StreamType
import com.superhexa.supervision.library.base.webviewhelper.WebPageHelper
import com.superhexa.supervision.library.db.AudioTranscriptionDbHelper
import com.superhexa.supervision.library.db.DeleteFileEvent
import com.superhexa.supervision.library.db.bean.MediaBean
import com.xiaomi.ai.capability.constant.Language
import com.xiaomi.ai.capability.request.model.Phrase
import com.xiaomi.aivs.capability.AiCapabilityWrapper
import com.xiaomi.wearable.core.gson
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import timber.log.Timber
import java.util.concurrent.atomic.AtomicInteger
import kotlin.coroutines.resume

/**
 * 类描述:音频转录、总结
 * 创建日期: 2025/3/14 on 15:43
 * 作者: GeYaoXiang
 */
class AudioTranscriptionViewModel :
    BaseMVIViewModel<AudioTranscriptionState, AudioTranscriptionEffect, AudioTranscriptionEvent>(),
    TranscriptionStateListener {

    val controller = AudioPlayerController(LibBaseApplication.instance)
    private val aiCapability = AiCapabilityWrapper.INSTANCE
    private val recordAudioService = RecordAudioService.getInstance()
    private val notificationManager = TranscriptionNotificationManager.getInstance()
    private val _effect = MutableSharedFlow<AudioTranscriptionEffect>(replay = 1)
    val effect: SharedFlow<AudioTranscriptionEffect> = _effect
    private val fastTime: Long = 10000L // 快进10s
    private val rewindTime: Long = -10000L // 快退10s

    private val _dialogState = MutableStateFlow(
        DialogState(
            mutableStateOf(false),
            mutableStateOf(false),
            mutableStateOf(false),
            mutableStateOf(false),
            mutableStateOf(false),
            mutableStateOf(false),
            mutableStateOf(false),
            mutableStateOf(false),
            mutableStateOf(false),
            mutableStateOf(false),
            mutableStateOf(Speaker(-1, false, "")),
            mutableStateOf(false),
            mutableStateOf(false)
        )
    )
    val dialogState: StateFlow<DialogState> get() = _dialogState
    val tabLiveData = mutableStateOf(Tab.Transcribe)
    val recordResultLiveData =
        MutableLiveData<RecordOptionResult>(RecordOptionResult.TranscribeOption(emptyList()))
    val isDistinguishSpeakers = mutableStateOf(true)
    val currentCurrentTemplate = mutableStateOf("abstractAutopilot")
    var currentLanguage = mutableStateOf("")
    var currentLanguageValue = mutableStateOf(Language.ZH_CN)
    val taskId = mutableStateOf("")
    val summaryTaskId = mutableStateOf("")

    var alphaState = mutableStateOf(true)

    // 转写的段落
    val transcribePhrasesList = mutableStateListOf<SpeakPhrase>()

    // 转写返回的内容生成对应的字符串
    var transcribePhrases: String = ""

    // 转写错误码
    var transcribeErrorCode: Int = 0

    // 是否在重新转写
    var isReTranscription: Boolean = false

    // 总结标题
    var transcriptionSummaryTitle = mutableStateOf("")

    // 总结内容
    var transcribeSummary = mutableStateOf("")

    // 总结使用的模版
    var summaryTemplate: String = "abstractAutopilot"

    // 总结错误码
    var summaryErrorCode: Int = 0

    // 是否在重新总结
    var isReSummary: Boolean = false

    // 说话人名称输入历史数据
    private var _speakerNameHistory = MutableStateFlow(
        MMKVUtils.decodeStringSet(ConstsConfig.SPEAKER_NAME_HISTORY_KEY)?.toList() ?: emptyList()
    )
    val speakerNameHistory
        get() = _speakerNameHistory

    fun addSingleSpeakerNameHistory(item: String) {
        val currentList = _speakerNameHistory.value.toMutableList()

        // 避免重复插入
        if (!currentList.contains(item)) {
            currentList.add(item)
            _speakerNameHistory.value = currentList
            MMKVUtils.encode(ConstsConfig.SPEAKER_NAME_HISTORY_KEY, currentList.toSet())
        }
    }

    fun addSpeakerNameHistory(items: List<String>) {
        val currentList = _speakerNameHistory.value.toMutableList()

        // 先把新条目从旧列表里移除，避免重复
        items.forEach { currentList.remove(it) }

        // 把新条目加到最前面，顺序保持items原顺序
        currentList.addAll(0, items)

        _speakerNameHistory.value = currentList
        MMKVUtils.encode(ConstsConfig.SPEAKER_NAME_HISTORY_KEY, currentList.toSet())
    }

    fun deleteSingleSpeakerNameHistory(item: String) {
        val currentList = _speakerNameHistory.value.toMutableList()
        if (currentList.remove(item)) {
            _speakerNameHistory.value = currentList
            MMKVUtils.encode(ConstsConfig.SPEAKER_NAME_HISTORY_KEY, currentList.toSet())
        }
    }

    fun deleteAllSpeakerNameHistory() {
        _speakerNameHistory.value = emptyList()
        MMKVUtils.removeKey(ConstsConfig.SPEAKER_NAME_HISTORY_KEY)
    }

    val webViewState = mutableStateOf<WebViewState?>(null)

    // 总结截图需要使用当前view中的webPageHelper的实例，跟随生命周期销毁
    var webPageHelper: WebPageHelper? = null
        private set

    fun setWebPageHelper(helper: WebPageHelper?) {
        this.webPageHelper = helper
    }

    // 倍速播放
    val playbackSpeed = MutableStateFlow(1.0f)
    val speedOptions = listOf(3.0f, 2.0f, 1.5f, 1.0f, 0.5f)

    // 高亮
    val highlightedPhraseId = mutableStateOf<Long?>(null)

    // 高亮句子的时间戳
    val highlightedSentenceOffset = mutableStateOf<Long?>(null)
    private var pendingSeekProgress = mutableStateOf<Long?>(null)

    // 判断是否滑动进度条
    val isDragging = mutableStateOf(false)

    // 标记 NewTranslateDialog 是否已显示
    val _hasShownTranslateDialog = mutableStateOf(false)
    val hasShownTranslateDialog: State<Boolean> get() = _hasShownTranslateDialog

    // 转写或总结错误
    private val _transcribeOrSummaryFail = MutableStateFlow<RecordSummaryFail?>(null)
    val transcribeOrSummaryFail: StateFlow<RecordSummaryFail?> get() = _transcribeOrSummaryFail

    // 删除isInitTranscribeOrSummary字段，改为通过数据状态判断

    val isShowLoading = mutableStateOf(false)
    private val _loadingType = MutableStateFlow<LoadingType?>(null)
    val loadingType
        get() = _loadingType

    private val _loadingDesc = MutableStateFlow<String?>(null)
    val loadingDesc
        get() = _loadingDesc

    fun setLoading(
        isLoading: Boolean,
        type: LoadingType? = null,
        desc: String? = null
    ) {
        isShowLoading.value = isLoading
        _loadingType.value = type
        _loadingDesc.value = desc
    }

    var imageCreateJob: Job? = null

    val showWebLoading = mutableStateOf(true)
    private val mutex = Mutex()
    private var deferredResult: CompletableDeferred<String>? = null

    data class WebViewState(
        val webView: WebView,
        val isInitialized: Boolean
    )

    data class Speaker(
        val objId: Long,
        var change: Boolean,
        var name: String,
        var isFocusSpeaker: Boolean = false
    )

    data class SpeakPhrase(
        val objId: Long,
        val speakName: String,
        val phrase: Phrase,
        var isFocusSpeaker: Boolean = false
    )

    sealed class RecordSummaryFail(val code: Int) {
        data class TranslateFail(val failCode: Int) : RecordSummaryFail(failCode)
        data class SummaryFail(val failCode: Int) : RecordSummaryFail(failCode)
    }

    enum class Tab {
        Transcribe,
        Summary
    }

    sealed class RecordOptionResult {
        // 加载
        data class LoadingOption(val transcribeLoading: Boolean, val summaryLoading: Boolean) :
            RecordOptionResult()

        // 转写
        data class TranscribeOption(val phrase: List<SpeakPhrase>) : RecordOptionResult()

        // 总结
        data class SummaryOption(
            val summary: String,
            val template: String,
            val forceRender: Boolean = false
        ) : RecordOptionResult()

        // 请求完成
        object FinishComplete : RecordOptionResult()
    }

    override fun initUiState() = AudioTranscriptionState()

    // ========== TranscriptionStateListener 接口实现 ==========

    override fun onTranscriptionStarted(taskId: String, filePath: String) {
        // 检查是否是当前音频文件的转写
        if (mState.value.currentItem?.path == filePath) {
            Timber.i("当前音频转写开始: taskId=$taskId, filePath=$filePath")
            this.taskId.value = taskId

            // 注意：数据库更新现在由 BackgroundTranscriptionService 处理
            // 这里只更新UI状态，避免重复数据库操作
            Timber.i("转写开始 - 数据库更新由后台服务处理，UI监听者只更新状态")
        }
    }

    override fun onTranscriptionSuccess(taskId: String, fileId: String, filePath: String, phrases: List<Phrase>) {
        // 检查是否是当前音频文件的转写
        if (mState.value.currentItem?.path == filePath) {
            Timber.i("当前音频转写完成: taskId=$taskId, fileId=$fileId, filePath=$filePath, phrases=${phrases.size}")
            mState.value.audioBean?.fileIdInCloud = fileId

            // 注意：数据库更新现在由 BackgroundTranscriptionService 处理
            // 这里只更新UI状态和处理总结逻辑
            updateStateAndHandleSummary(phrases, isReTranscription)
        }
    }

    override fun onTranscriptionFailed(
        taskId: String,
        filePath: String,
        errorCode: Int,
        errorMessage: String?
    ) {
        // 检查是否是当前音频文件的转写
        if (mState.value.currentItem?.path == filePath) {
            Timber.e("当前音频转写失败: taskId=$taskId, filePath=$filePath, code=$errorCode, message=$errorMessage")
            transFail(errorCode)
        }
    }

    override fun onPollingStatusChanged(taskId: String, filePath: String, isPolling: Boolean) {
        // 检查是否是当前音频文件的转写
        if (mState.value.currentItem?.path == filePath) {
            Timber.i("当前音频轮询状态变化: taskId=$taskId, filePath=$filePath, isPolling=$isPolling")
            // 注意：不在这里更新UI状态，因为转写成功/失败的回调已经处理了UI状态更新
            // 这里只记录轮询状态变化，避免覆盖正确的UI状态
        }
    }

    // ========== 录音上传相关回调方法 ==========

    override fun onUploadStarted(taskId: String, filePath: String) {
        // 检查是否是当前音频文件的上传
        if (mState.value.currentItem?.path == filePath) {
            Timber.i("当前音频上传开始: taskId=$taskId, filePath=$filePath")
            // 可以在这里更新UI状态，显示上传进度
        }
    }

    override fun onUploadSuccess(taskId: String, filePath: String, fileId: String?) {
        // 检查是否是当前音频文件的上传
        if (mState.value.currentItem?.path == filePath) {
            Timber.i("当前音频上传成功: taskId=$taskId, filePath=$filePath, fileId=$fileId")
            // 可以在这里更新UI状态，显示上传完成
            mState.value.audioBean?.fileIdInCloud = fileId
        }
    }

    override fun onUploadFailed(taskId: String, filePath: String, errorCode: Int, errorMessage: String?) {
        // 检查是否是当前音频文件的上传
        if (mState.value.currentItem?.path == filePath) {
            Timber.e("当前音频上传失败: taskId=$taskId, filePath=$filePath, errorCode=$errorCode, errorMessage=$errorMessage")
            // 可以在这里更新UI状态，显示上传失败
            // 注意：上传失败后，转写也会失败，会在 onTranscriptionFailed 中处理
        }
    }

    // ========== 录音总结相关回调方法 ==========

    override fun onSummaryStarted(taskId: String, filePath: String, summaryTaskId: String, template: String) {
        // 检查是否是当前音频文件的总结
        if (mState.value.currentItem?.path == filePath) {
            Timber.i("当前音频总结开始: taskId=$taskId, filePath=$filePath, summaryTaskId=$summaryTaskId, template=$template")

            // 更新UI状态显示总结加载中
            recordResultLiveData.postValue(
                RecordOptionResult.LoadingOption(
                    showTranscribeLoading(),
                    true
                )
            )
        }
    }

    override fun onSummarySuccess(
        taskId: String,
        filePath: String,
        summaryTaskId: String,
        title: String,
        content: String,
        focusSpeakerSummaryContent: String?,
        template: String
    ) {
        // 检查是否是当前音频文件的总结
        if (mState.value.currentItem?.path == filePath) {
            Timber.i("当前音频总结完成: taskId=$taskId, filePath=$filePath, summaryTaskId=$summaryTaskId, title=$title")

            // 更新UI状态
            isReSummary = false
            summaryErrorCode = 0
            transcriptionSummaryTitle.value = title
            transcribeSummary.value = content
            mState.value.audioBean?.let {
                it.summaryStr = content
                it.focusSpeakerSummaryStr = focusSpeakerSummaryContent
            }

            recordResultLiveData.postValue(
                RecordOptionResult.SummaryOption(
                    content,
                    template,
                    true
                )
            )
        }
    }

    override fun onSummaryFailed(taskId: String, filePath: String, summaryTaskId: String, errorCode: Int, template: String) {
        // 检查是否是当前音频文件的总结
        if (mState.value.currentItem?.path == filePath) {
            Timber.e("当前音频总结失败: taskId=$taskId, filePath=$filePath, summaryTaskId=$summaryTaskId, errorCode=$errorCode")

            // 更新UI状态
            isReSummary = false
            summaryErrorCode = errorCode
            _transcribeOrSummaryFail.value = RecordSummaryFail.SummaryFail(errorCode)

            if (showTranscribeLoading()) {
                recordResultLiveData.postValue(
                    RecordOptionResult.LoadingOption(
                        transcribeLoading = true,
                        false
                    )
                )
            } else {
                recordResultLiveData.postValue(RecordOptionResult.FinishComplete)
            }
        }
    }

    @Suppress("ComplexMethod")
    override fun reduce(oldState: AudioTranscriptionState, event: AudioTranscriptionEvent) {
        Timber.i("reduce $event")
        when (event) {
            is AudioTranscriptionEvent.Init -> {
                viewModelScope.launch(Dispatchers.Main) {
                    mState.value.audioBean?.path?.let {
                        controller.prepare(it.toUri())
                        // 设置当前在前台的文件路径，避免发送不必要的通知
                        notificationManager.setForegroundFilePath(it)
                    }
                    taskRetry()
                }
            }

            is AudioTranscriptionEvent.PlayOrPause -> handlePlayOrPause()
            is AudioTranscriptionEvent.Pause -> controller.pause()
            is AudioTranscriptionEvent.PlayClipForDuration -> {
                controller.playClipForDuration(
                    viewModelScope,
                    event.offsetMs,
                    event.durationMs
                )
            }
            is AudioTranscriptionEvent.SeekTo -> {
                val seekTime = (event.progress * controller.duration.value).toLong()
                pendingSeekProgress.value = seekTime
                controller.seekTo(seekTime)
            }

            is AudioTranscriptionEvent.Stop -> controller.stop()
            is AudioTranscriptionEvent.ShareItem -> shareItem(event.context, event.shareType)
            is AudioTranscriptionEvent.ShareLink -> requestShareLink(event)
            is AudioTranscriptionEvent.RequestSummary -> {
                Timber.i("RequestSummary template:${event.template}")
                requestSummary(event.template, true)
            }

            AudioTranscriptionEvent.RequestTranscribe -> startTranscribeRequest()
            AudioTranscriptionEvent.ReTranscribe -> startTranscribeRequest(true)
            AudioTranscriptionEvent.CopyTranscribed -> {
                val transcribeBuilder = StringBuilder()
                transcribePhrasesList.forEach {
                    transcribeBuilder
                        .append(it.speakName).append(":")
                        .append(it.phrase.text)
                        .append("\n")
                }
                copyText(transcribeBuilder.toString().ifEmpty { transcribePhrases })
            }
            AudioTranscriptionEvent.CopySummary -> copyText(transcribeSummary.value)
            // 快进
            AudioTranscriptionEvent.FastForward -> {
                handleSeek(fastTime)
            }
            // 快退
            AudioTranscriptionEvent.Rewind -> {
                handleSeek(rewindTime)
            }
            // 倍速播放
            is AudioTranscriptionEvent.SpeedSelected -> {
                playbackSpeed.value = event.speed
                controller.setPlaybackSpeed(event.speed)
            }
            // 仅用于点击时间戳跳转播放
            is AudioTranscriptionEvent.Play -> {
                handlePlay()
            }
        }
    }

    // 处理快进快退更新后的时间戳位置
    private fun handleSeek(delta: Long) {
        val duration = controller.duration.value
        val currentPosition = controller.currentPosition.value
        if (duration < 0 || currentPosition < 0) {
            return
        }
        val newPosition = (currentPosition + delta).coerceIn(0L..duration)
        controller.seekTo(newPosition)
    }

    fun showSpeedMenuDialog(show: Boolean) {
        _dialogState.value = _dialogState.value.copy(
            isShowSpeedMenu = mutableStateOf(show) // 更新菜单显示状态
        )
    }

    fun resetWebView() {
        val oldWebViewState = webViewState.value
        oldWebViewState?.webView?.apply {
            reload()
        }
        webViewState.value = null
        webPageHelper = null
    }

    private fun taskRetry() {
        Timber.i(
            """
            taskRetry: ${taskId.value}
            summaryTaskId: ${summaryTaskId.value}
            audioBean: ${mState.value.audioBean}
            """.trimIndent()
        )
        if (showTranscribeLoading()) {
            isReTranscription = true
            isReSummary = showSummaryLoading()
            recordResultLiveData.postValue(
                RecordOptionResult.LoadingOption(true, isReSummary)
            )
            val mediaBean = mState.value.currentItem
            val transcriptionId = taskId.value
            Timber.i("taskRetry getResult transcribeId:$transcriptionId")

            if (recordAudioService.isFakeTaskId(transcriptionId)) {
                // fake_开头表示服务端没有下发taskId，是本地生成的一个taskId为了显示loading
                val isUploading = AiCapabilityWrapper.uploadingFileMap[mediaBean?.path]
                Timber.i("taskRetry uploading:$isUploading")
                if (isUploading == true) {
                    mediaBean?.path?.let { filePath ->
                        recordAudioService.handleUploadingFile(
                            filePath,
                            mediaBean,
                            summaryTemplate
                        )
                    }
                } else {
                    // 与ios保持一致,重新调用转写接口上传文件，此处需要sdk支持断点续传
                    startTranscribeRequest(isNeedRequestSummary())
                }
            } else {
                // 已有真实taskId，直接使用后台服务轮询结果
                mediaBean?.path?.let { filePath ->
                    recordAudioService.startPollingWithTaskId(
                        transcriptionId,
                        filePath,
                        mediaBean,
                        summaryTemplate
                    )
                }
            }
        }
        if (showSummaryLoading()) {
            Timber.i("taskRetry 重新请求总结 summaryTemplate:$summaryTemplate")
            val currentItem = mState.value.currentItem
            if (currentItem != null && transcribePhrases.isNotEmpty()) {
                // 使用后台服务重新请求总结
                recordAudioService.requestSummary(
                    transcriptionTaskId = taskId.value,
                    filePath = currentItem.path,
                    mediaBean = currentItem,
                    template = summaryTemplate,
                    transcribeContent = generateNewTranscribeContentForRequest(),
                    speakerFocusJson = generateFocusSpeakersNameJson(),
                    isReSummary = true
                )
            } else {
                Timber.w("taskRetry: currentItem或转写内容为空，无法重新总结")
            }
        }
    }

    init {
        // 注册后台转写服务监听者
        recordAudioService.registerListener(this)

        // 监听播放器状态变化
        viewModelScope.launch {
            controller.playbackState.collect { state ->
                when (state) {
                    is AudioPlayerController.PlaybackState.PLAYING -> setState(
                        mState.value.copy(
                            isPlaying = true,
                            playStatus = PlayState.PLAY
                        )
                    )

                    is AudioPlayerController.PlaybackState.PAUSED -> setState(
                        mState.value.copy(
                            isPlaying = false,
                            playStatus = PlayState.PAUSE
                        )
                    )

                    is AudioPlayerController.PlaybackState.STOPPED -> {
                        setState(
                            mState.value.copy(isPlaying = false, playStatus = PlayState.STOP)
                        )
                        controller.stop()
                    }

                    is AudioPlayerController.PlaybackState.READY -> {
                        Timber.i("file ready")
                        controller.getDuration()
                    }

                    else -> {}
                }
            }
        }

        viewModelScope.launch {
            controller.currentPosition.collect { position ->
//                Timber.i("currentPosition $position")
                if (position >= 0) {
                    // 太早获取文件时长会返回负数，这里当开始播放后再获取一次
                    controller.getDuration()
                    setState(mState.value.copy(playbackProgress = position))
                    updateHighlightedPhrase(position)
                }
            }
        }

        // 监听总时长
        viewModelScope.launch {
            controller.duration.collect { duration ->
                Timber.i("duration $duration")
                if (duration > 0) {
                    setState(mState.value.copy(totalDuration = duration))
                }
            }
        }
    }

    @Suppress("LoopWithTooManyJumpStatements")
    private fun updateHighlightedPhrase(position: Long) {
        var foundPhraseId: Long? = null
        var foundSentenceOffset: Long? = null

        var lastPhraseId: Long? = null
        var lastSentenceOffset: Long? = null
        var maxStartTime = -1L // 用于记录position之前最大的开始时间

        // 遍历段落以及段落中的语句，根据传入当前时间的position去匹配所在语句
        for (speakPhrase in transcribePhrasesList) {
            val sentences = speakPhrase.phrase.sentences
            if (sentences.isNullOrEmpty()) {
                break
            }
            for (sentence in sentences) {
                val sentenceStartTime = sentence.offsetMilliseconds
                val sentenceEndTime = sentenceStartTime + sentence.durationMilliseconds

                // 如果position在句子时间段内，直接匹配
                if (position in sentenceStartTime..sentenceEndTime) {
                    foundPhraseId = speakPhrase.objId
                    foundSentenceOffset = sentenceStartTime
                    break
                }

                // 记录离当前position最近的前一语句开始时间，如果position落在空白时间，取上一语句高亮
                if (sentenceStartTime <= position) {
                    if (sentenceStartTime > maxStartTime) {
                        maxStartTime = sentenceStartTime
                        lastPhraseId = speakPhrase.objId
                        lastSentenceOffset = sentenceStartTime
                    }
                }
            }
            if (foundPhraseId != null) break
        }
        // 如果没有找到当前播放位置的句子，使用上一句的高亮
        if (foundPhraseId == null) {
            foundPhraseId = lastPhraseId
            foundSentenceOffset = lastSentenceOffset
        }
        Timber.i("Current position: $position, Found: $foundSentenceOffset")
        // 用于更新UI
        highlightedPhraseId.value = foundPhraseId
        highlightedSentenceOffset.value = foundSentenceOffset
    }

    private fun handlePlay() {
        mState.value.currentItem?.apply {
            when (mState.value.playStatus) {
                PlayState.PAUSE -> {
                    controller.play(viewModelScope)
                    Timber.i("handlePlay: play")
                }
                else -> {
                    playNewMedia(this)
                    Timber.i("handlePlay: state:${mState.value.playStatus}")
                }
            }
        }
    }

    private fun handlePlayOrPause() {
        mState.value.currentItem?.apply {
            when (mState.value.playStatus) {
                PlayState.PLAY -> {
                    controller.pause()
                    Timber.i("handlePlayOrPause: pause")
                }

                PlayState.PAUSE -> {
                    controller.play(viewModelScope)
                    Timber.i("handlePlayOrPause: play")
                }

                else -> {
                    playNewMedia(this)
                    Timber.i("handlePlayOrPause: state:${mState.value.playStatus}")
                }
            }
        }
    }

    private fun playNewMedia(bean: MediaBean) {
        controller.stop()
        controller.prepare(bean.path.toUri())

        controller.setPlaybackSpeed(playbackSpeed.value)

        pendingSeekProgress.value?.let { progress ->
            controller.seekTo(progress)
            pendingSeekProgress.value = null
        }
        controller.play(viewModelScope)
        Timber.i("playNewMedia: $bean")
    }

    private fun shareItem(context: Context, shareType: AudioTranscriptionShareType) {
        when (shareType) {
            AudioTranscriptionShareType.AUDIO -> {
                viewModelScope.launch {
                    mState.value.currentItem?.let {
                        _effect.emit(AudioTranscriptionEffect.ShareAudioItem(it, getUniqueId()))
                    }
                }
            }

            AudioTranscriptionShareType.IMAGE_OF_TRANSCRIBED -> {
                imageCreateJob?.cancel()
                imageCreateJob = viewModelScope.launch(Dispatchers.IO) {
                    ImageHelper.generateTranscriptBitmap(
                        context = context,
                        phrases = transcribePhrasesList,
                        onResult = { result, bitmap ->
                            if (result) {
                                viewModelScope.launch {
                                    _effect.emit(
                                        AudioTranscriptionEffect.ShareImage(
                                            image = bitmap,
                                            type = ImageType.TRANSCRIBED,
                                            success = true
                                        )
                                    )
                                }
                            } else {
                                viewModelScope.launch {
                                    _effect.emit(
                                        AudioTranscriptionEffect.ShareImage(
                                            image = null,
                                            type = ImageType.TRANSCRIBED,
                                            success = false
                                        )
                                    )
                                }
                            }
                        }
                    )
                }
            }

            AudioTranscriptionShareType.IMAGE_OF_SUMMARY -> {
                imageCreateJob?.cancel()
                imageCreateJob = viewModelScope.launch {
                    webViewState.value.let { viewState ->
                        if (viewState?.isInitialized == false || viewState?.webView == null) {
                            _effect.emit(
                                AudioTranscriptionEffect.ShareImage(
                                    image = null,
                                    type = ImageType.SUMMARY,
                                    success = false
                                )
                            )
                        } else {
                            webPageHelper?.let { helper ->
                                val listener = object : WebPageHelper.IFileConvertedListener {
                                    override fun onConverted(result: Triple<Boolean, String?, Bitmap?>) {
                                        val bitmap = result.third
                                        viewModelScope.launch {
                                            val bitmapWithWatermark = bitmap?.let {
                                                ImageHelper.addWatermarkToBitmap(
                                                    context = context,
                                                    srcBitmap = it
                                                )
                                            }
                                            _effect.emit(
                                                AudioTranscriptionEffect.ShareImage(
                                                    image = bitmapWithWatermark,
                                                    type = ImageType.SUMMARY,
                                                    success = bitmapWithWatermark != null
                                                )
                                            )
                                        }
                                        // 获取以后移除监听
                                        WebPageHelper.removeFileConvertedListener()
                                    }
                                }
                                WebPageHelper.setFileConvertedListener(listener)
                                helper.requestImageOfSummary(
                                    streamType = StreamType.SUMMARY_SHARE_IMAGE,
                                    timestamp = System.currentTimeMillis().toString()
                                )
                            }
                        }
                    }
                }
            }

            else -> Unit
        }
    }

    private fun deleteItem() {
        viewModelScope.launch {
            mState.value.currentItem?.let {
                _effect.emit(AudioTranscriptionEffect.Delete)
            }
        }
    }

    private fun getUniqueId(): Int {
        return System.currentTimeMillis().toInt()
    }

    fun toDeleteMediaFile(isDeleteGallery: Boolean = false) = viewModelScope.launch {
        val curMediaBean = mState.value.currentItem ?: return@launch
        Timber.i("toDeleteMediaFile isDeleteGallery:$isDeleteGallery ,curMediaBean: $curMediaBean")
        EventBus.getDefault().post(DeleteFileEvent(isDeleteGallery, curMediaBean))
        deleteItem()
    }

    fun updateDistinguishSpeakers(value: Boolean) {
        Timber.i("updateDistinguishSpeakers $value")
        isDistinguishSpeakers.value = value
    }

    fun switchTab(tab: Tab) {
        val recordState = recordResultLiveData.value
        if (recordState !is RecordOptionResult.LoadingOption) {
            if (tab == Tab.Transcribe) {
                recordResultLiveData.value =
                    RecordOptionResult.TranscribeOption(transcribePhrasesList)
            } else {
                recordResultLiveData.value =
                    RecordOptionResult.SummaryOption(transcribeSummary.value, summaryTemplate)
            }
        }
        tabLiveData.value = tab
    }

    /**
     * 设置初始Tab，用于通知跳转时指定显示的Tab
     */
    fun setInitialTab(tab: Tab) {
        tabLiveData.value = tab
        Timber.i("设置初始Tab: $tab")
    }

    private fun showTranscribeLoading(): Boolean {
        val taskIdValue = taskId.value
        val transcribePhrase = mState.value.audioBean?.srcStr
        Timber.d(
            "showTranscribeLoading called reTranscribe:$isReTranscription," +
                " taskId:${taskIdValue.isNotEmpty()}," +
                " phrase:${transcribePhrase.isNullOrEmpty()}"
        )
        return isReTranscription || (taskIdValue.isNotEmpty() && transcribePhrase.isNullOrEmpty())
    }

    private fun showSummaryLoading(): Boolean {
        val summaryId = summaryTaskId.value
        val summary = mState.value.audioBean?.summaryStr
        Timber.d(
            "showSummaryLoading called reSummary:$isReSummary," +
                " summaryId:${summaryId.isNotEmpty()}," +
                " summary:${summary.isNullOrEmpty()}," +
                " summaryErrorCode:$summaryErrorCode"
        )
        return isReSummary || (summaryId.isNotEmpty() && summary.isNullOrEmpty() && summaryErrorCode == 0)
    }

    fun fillPhraseList(phrases: List<SpeakPhrase>) {
        Timber.d("fillPhraseList called ${phrases.size}")
        if (phrases.isEmpty()) {
            if (taskId.value.isEmpty() &&
                mState.value.audioBean?.srcStr.isNullOrEmpty()
            ) {
                Timber.d("fillPhraseList called post emptyList")
                recordResultLiveData.postValue(RecordOptionResult.TranscribeOption(emptyList()))
            }
        } else {
            phrases.forEach {
                SpeakerTextDefaults.setSpeakerColor(it.speakName)
            }
            transcribePhrasesList.clear()
            transcribePhrasesList.addAll(phrases)
            transcribePhrases = phrases.joinToString(separator = "") { it.phrase.text }
            isReTranscription = false

            recordResultLiveData.postValue(RecordOptionResult.TranscribeOption(phrases))
            deferredResult?.complete(transcribePhrases)
            deferredResult = null
        }
    }

    fun fillSummary(summary: String) {
        transcribeSummary.value = summary
    }

    fun reTranscription() {
        _transcribeOrSummaryFail.value = null
    }

    fun showTranscriptionSettingsDialog(value: Boolean) {
        dialogState.value.isShowDialog.value = value
    }

    fun showNewTranslateDialog(value: Boolean) {
        dialogState.value.isShowNewTranslateDialog.value = value
    }

    fun showReSummarizeDialog(value: Boolean) {
        dialogState.value.isShowReSummarize.value = value
    }

    fun showReTranscribeDialog(value: Boolean) {
        dialogState.value.isShowReTranscribe.value = value
    }

    fun showSelectLanguageDialog(value: Boolean) {
        dialogState.value.isShowSelectLanguage.value = value
    }

    fun showDeleteDialog(show: Boolean) {
        dialogState.value.isShowDelete.value = show
    }

    fun showGuideDialog(show: Boolean) {
        dialogState.value.showGuideDialog.value = show
    }

    fun showMenu(show: Boolean) {
        dialogState.value.showMenu.value = show
    }

    fun showShareMenu(show: Boolean) {
        dialogState.value.showShareMenu.value = show
    }

    fun showShareLinkMenu(show: Boolean) {
        dialogState.value.showShareLinkMenu.value = show
    }

    fun showFixSpeakerName(speaker: Speaker) {
        dialogState.value.showFixSpeakerName.value = speaker
    }

    fun showQuickFixAllDialog(show: Boolean) {
        dialogState.value.showQuickFixAllDialog.value = show
    }

    suspend fun changeSpeakerName(
        srcSpeakerName: String,
        objIds: List<Long>,
        dstSpeakerName: String,
        fixAll: Boolean,
        onCompleted: (() -> Unit)? = null
    ) {
        val updateList = AudioTranscriptionDbHelper.updateSpeakName(
            mState.value.currentItem!!,
            objIds,
            srcSpeakerName = srcSpeakerName,
            dstSpeakerName = dstSpeakerName,
            fixAll
        )
        if (updateList.isNotEmpty()) {
            SpeakerTextDefaults.reset()
            val newList = transcribePhrasesList.map { originalItem ->
                if (updateList.contains(originalItem.objId)) {
                    originalItem.copy(speakName = dstSpeakerName)
                } else {
                    originalItem
                }
            }.toList()

            withContext(Dispatchers.Main) {
                fillPhraseList(newList)
            }
            onCompleted?.invoke()
        }
    }

    suspend fun markAsFocusSpeaker(
        objIds: List<Long>,
        srcSpeakerName: String,
        isFocusSpeaker: Boolean,
        fixAll: Boolean
    ) {
        mState.value.currentItem?.let { bean ->
            val updateList = AudioTranscriptionDbHelper.updateIsFocusSpeaker(
                bean = bean,
                objIds = objIds,
                srcSpeakerName = srcSpeakerName,
                isFocusSpeaker = isFocusSpeaker,
                fixAll = fixAll
            )
            if (updateList.isNotEmpty()) {
                val newList = transcribePhrasesList.map { originalItem ->
                    if (updateList.contains(originalItem.objId)) {
                        originalItem.copy(isFocusSpeaker = isFocusSpeaker)
                    } else {
                        originalItem
                    }
                }.toList()

                withContext(Dispatchers.Main) {
                    fillPhraseList(newList)
                }
            }
        }
    }

    fun getIsFocusSpeakerByName(name: String): Boolean {
        return transcribePhrasesList.find { it.speakName == name }?.isFocusSpeaker ?: false
    }

    private fun copyText(string: String?, descResId: Int = R.string.tips_copy_success_two) {
        string?.let {
            InputUtil.copy2ClipBoard(LibBaseApplication.instance, string)
            LibBaseApplication.instance.toast(descResId)
        }
    }

    private fun transFail(code: Int) {
        Timber.e("转写失败: $code")
        transcribeErrorCode = code
        _transcribeOrSummaryFail.value = RecordSummaryFail.TranslateFail(code)
    }

    /**
     * 是否展示重新转写和重新总结
     */
    fun isShowReOption(): Boolean {
        return transcribePhrasesList.isNotNullOrEmpty() ||
            transcribeSummary.value.isNotNullOrEmpty() ||
            summaryErrorCode != 0
    }

    /**
     * 展示tab
     */
    fun isCanShowTab(): Boolean {
        val hasAnyContent = transcribePhrasesList.isNotEmpty() ||
            transcribeSummary.value.isNotEmpty() ||
            summaryErrorCode != 0
        val isNotInitialLoading = taskId.value.isEmpty() || hasAnyContent
        val result = isNotInitialLoading && hasAnyContent

        Timber.d(
            "isCanShowTab called >>> $result" +
                ", hasAnyContent:$hasAnyContent" +
                ", isNotInitialLoading:$isNotInitialLoading" +
                ", phraseList:${transcribePhrasesList.isNotEmpty()}" +
                ", summary:${transcribeSummary.value.isNotEmpty()}" +
                ", summaryErrorCode:$summaryErrorCode"
        )
        return result
    }

    /**
     * 更新UI状态并处理总结逻辑（不更新数据库，因为后台服务已处理）
     * @param phrases 转写的内容
     * @param isReTranscribe 是否是重新转写
     */
    private fun updateStateAndHandleSummary(phrases: List<Phrase>, isReTranscribe: Boolean) {
        Timber.i("转写成功（后台服务已更新数据库）:isReTranscribe:$isReTranscribe,size:${phrases.size}")
        if (phrases.isEmpty()) {
            // 转写完成，重置重新转写状态
            isReTranscription = false

            if (showSummaryLoading()) {
                recordResultLiveData.postValue(
                    RecordOptionResult.LoadingOption(
                        transcribeLoading = false,
                        true
                    )
                )
            } else {
                recordResultLiveData.postValue(RecordOptionResult.FinishComplete)
            }
            transFail(204)
        } else {
            // 从数据库获取objId和speakerName信息（后台服务已更新数据库）
            viewModelScope.launch(Dispatchers.IO) {
                val audioTranscriptionBeanList =
                    AudioTranscriptionDbHelper.findCorrespondBean(mState.value.currentItem!!)
                if (audioTranscriptionBeanList.isNotEmpty()) {
                    val speakPhraseList = mutableListOf<SpeakPhrase>()
                    audioTranscriptionBeanList.forEachIndexed { index, transcriptionBean ->
                        if (index < phrases.size) {
                            val speakPhrase = SpeakPhrase(
                                transcriptionBean?.objId ?: 0L,
                                transcriptionBean?.speakerName ?: "",
                                phrases[index]
                            )
                            speakPhraseList.add(speakPhrase)
                        }
                    }

                    withContext(Dispatchers.Main) {
                        fillPhraseList(speakPhraseList)

                        // 更新状态
                        setState(
                            mState.value.copy(
                                audioBean = mState.value.audioBean?.copy(
                                    srcStr = gson.toJson(
                                        phrases
                                    )
                                )
                            )
                        )

                        // 转写完成，重置重新转写状态
                        isReTranscription = false

                        // 注意：不在这里发送UI状态更新，因为fillPhraseList已经处理了
                        // 自动总结逻辑现在由 BackgroundTranscriptionService 处理
                        Timber.i("转写UI状态更新完成，自动总结由后台服务处理")
                    }
                }
            }
        }
    }

    /**
     * 是否调用总结接口，如果返回false表示调用，否则不调用
     */
    private fun isNeedRequestSummary(): Boolean {
        return !(isReSummary || (transcribeSummary.value.isEmpty() && summaryErrorCode == 0))
    }

    private fun requestSummary(template: String, reSummary: Boolean = false) {
        Timber.i("requestSummary isEmpty ${transcribePhrases.isEmpty()}, template:$template, reSummary:$reSummary")
        summaryTemplate = template

        val currentItem = mState.value.currentItem
        if (currentItem == null) {
            Timber.w("requestSummary: currentItem is null")
            return
        }

        // 准备转写内容
        var transcribeContent = transcribePhrases
        if (transcribeContent.isEmpty()) {
            // 如果转写内容为空，等待转写完成
            LibBaseApplication.instance.toast(R.string.record_wait_transcribe_finish)
            viewModelScope.launch {
                mutex.withLock {
                    // 创建新的 Deferred
                    deferredResult = CompletableDeferred()
                    Timber.w("requestSummary wait transcribe content")
                    transcribeContent = deferredResult!!.await()

                    // 转写内容准备好后，调用后台服务
                    callBackgroundSummaryService(
                        currentItem,
                        template,
                        generateNewTranscribeContentForRequest(),
                        reSummary
                    )
                }
            }
        } else {
            // 转写内容已准备好，直接调用后台服务
            callBackgroundSummaryService(
                currentItem,
                template,
                generateNewTranscribeContentForRequest(),
                reSummary
            )
        }
    }

    private fun callBackgroundSummaryService(
        mediaBean: MediaBean,
        template: String,
        transcribeContent: String,
        reSummary: Boolean
    ) {
        Timber.i("调用后台总结服务: template=$template, reSummary=$reSummary, content.length=${transcribeContent.length}")

        if (reSummary) {
            // 重新总结时，先更新UI状态
            isReSummary = true
            mState.value.audioBean?.summaryStr = ""
            transcribeSummary.value = ""
            recordResultLiveData.postValue(
                RecordOptionResult.LoadingOption(
                    showTranscribeLoading(),
                    true
                )
            )
        }

        // 调用后台服务进行总结
        recordAudioService.requestSummary(
            transcriptionTaskId = taskId.value,
            filePath = mediaBean.path,
            mediaBean = mediaBean,
            template = template,
            transcribeContent = transcribeContent,
            speakerFocusJson = generateFocusSpeakersNameJson(),
            isReSummary = reSummary
        )
    }

    override fun onCleared() {
        super.onCleared()
        // 取消注册后台转写服务监听者
        recordAudioService.unregisterListener(this)
        // 清除前台文件路径，允许发送通知
        notificationManager.setForegroundFilePath(null)
        // 注意：不再需要销毁 transcriptionManager，因为后台服务有自己的生命周期管理
        Timber.i("AudioTranscriptionViewModel cleared")
    }

    private fun startTranscribeRequest(isReTranscribe: Boolean = false) {
        Timber.i("开始转写请求，isReTranscribe: $isReTranscribe")

        mState.value.currentItem?.let { mediaBean ->
            isReTranscription = isReTranscribe

            if (isReTranscribe) {
                // 重新转写，清空之前的结果
                mState.value.audioBean?.srcStr = ""
                transcribePhrases = ""
                // 清空总结相关状态，避免UI状态混乱
                isReSummary = false
                AudioTranscriptionDbHelper.clearTranscribeContent(mediaBean)
            }

            recordResultLiveData.postValue(
                RecordOptionResult.LoadingOption(
                    true,
                    showSummaryLoading()
                )
            )

            // 创建转写请求
            val request = AudioTranscriptionManager.TranscriptionRequest(
                filePath = mediaBean.path,
                language = currentLanguageValue.value,
                distinguishSpeakers = isDistinguishSpeakers.value,
                isReTranscribe = isReTranscribe
            )

            // 使用后台转写服务发起请求，这样即使退出界面也能继续轮询
            // 传递完整参数，让后台服务直接更新数据库
            val generatedTaskId = recordAudioService.startTranscription(
                request,
                mediaBean,
                summaryTemplate
            )
            taskId.value = generatedTaskId

            // 注意：数据库更新现在由后台服务处理，这里不再重复更新
        }
    }

    fun stopPlay() {
        Timber.i("stopPlay")
        controller.stop()
    }

    /**
     * @param event.shareOptions 1.总结 2.转写 3.录音音频
     * @param event.textOnly 是否只是分享到剪贴板
     **/
    private fun requestShareLink(
        event: AudioTranscriptionEvent.ShareLink
    ) {
        viewModelScope.launch(Dispatchers.IO) {
            val transcribeText = if (event.shareOptions.second) generateTranscribedRequestJson() else null
            val summaryText = if (event.shareOptions.first) generateSummaryRequestJson() else null
            val fileId = if (event.shareOptions.third) getFileId() else null
            aiCapability.transcribeShareRequest(
                transcribeText = transcribeText,
                summaryText = summaryText,
                fileId = fileId,
                onResult = { result ->
                    result.onSuccess { response ->
                        response.result.let {
                            if (!it.isNullOrBlank()) {
                                if (event.textOnly) {
                                    val url = WebPageHelper.getShareLink(it)
                                    setLoading(false)
                                    copyText(url, R.string.text_copy_link_success)
                                } else {
                                    viewModelScope.launch(Dispatchers.Main) {
                                        _effect.emit(AudioTranscriptionEffect.ShareLink(it, true))
                                    }
                                }
                            } else {
                                viewModelScope.launch(Dispatchers.Main) {
                                    _effect.emit(AudioTranscriptionEffect.ShareLink("", false))
                                }
                            }
                        }
                    }.onFailure { response ->
                        viewModelScope.launch(Dispatchers.Main) {
                            _effect.emit(AudioTranscriptionEffect.ShareLink("", false))
                        }
                    }
                }
            )
        }
    }

    fun generateTranscribedRequestJson(): String {
        val list = transcribePhrasesList.map {
            TranscribedRequestContent(
                speakerName = it.speakName,
                speakerColor = SpeakerTextDefaults.getColor(it.speakName).toArgb().toString(),
                speakContent = it.phrase.text
            )
        }
        return list.toJson()
    }

    fun generateNewTranscribeContentForRequest(): String {
        return transcribePhrasesList.joinToString(separator = "\n") {
            "${it.speakName}：\n${it.phrase.text}"
        }
    }

    // 因需求变更，现在请求H5页面的Summary需要新增一些参数，用json传
    fun generateSummaryRequestJson(): String {
        val title = mState.value.currentItem?.fileName ?: transcriptionSummaryTitle.value
        val duration = mState.value.currentItem?.let { bean ->
            DateTimeUtils.formatDuration(bean.duration * 1000)
        } ?: ""
        val date = mState.value.currentItem?.let { bean ->
            DateTimeUtils.convertTimeStampToDateString(bean.fileAdded)
        } ?: ""

        return SummaryRequestContent(
            title = title,
            tag = getTemplateItemByValue(summaryTemplate).displayName,
            duration = duration,
            date = date,
            speakerFocusContent = generateFocusSpeakersContentJson(),
            content = transcribeSummary.value
        ).toJson()
    }

    private fun generateFocusSpeakersNameJson(): String? {
        return transcribePhrasesList
            .filter { it.isFocusSpeaker }
            .map { it.speakName }
            .distinct() // 去重
            .takeIf { it.isNotNullOrEmpty() }
            ?.toJson()
    }

    // 用于传给h5显示重点说话人总结
    private fun generateFocusSpeakersContentJson(): String? {
        val names = transcribePhrasesList
            .filter { it.isFocusSpeaker }
            .map { it.speakName }
            .distinct()

        if (names.isEmpty()) return null

        val summaries: List<String> = runCatching {
            val type = object : TypeToken<List<String>>() {}.type
            val jsonStr = mState.value.audioBean?.focusSpeakerSummaryStr
            if (jsonStr.isNotNullOrEmpty()) {
                gson.fromJson<List<String>>(jsonStr, type)
            } else {
                emptyList()
            }
        }.getOrElse {
            emptyList()
        }

        // 组装 Map
        val resultList = names.zip(summaries).map { (speakerName, content) ->
            TranscribedRequestContent(
                speakerName = speakerName,
                speakerColor = SpeakerTextDefaults.getColor(speakerName).toArgb().toString(),
                speakContent = content
            )
        }

        return if (resultList.isNotEmpty()) resultList.toJson() else null
    }

    private fun getFileId(): String? = mState.value.audioBean?.fileIdInCloud

    fun containsFileId(): Boolean = getFileId().isNotNullOrEmpty()

    suspend fun checkSensitiveContent(inputText: String): Pair<String, Boolean> {
        return suspendCancellableCoroutine { cont ->
            aiCapability.checkInputText(
                inputText = inputText,
                onResult = { success, res ->
                    val result = if (success) {
                        Pair(inputText, res.data?.isBlock == false)
                    } else {
                        // TODO 看看是不是要根据错误码去提示用户
                        Pair(inputText, false)
                    }
                    cont.resume(result)
                }
            )
        }
    }
}

@Keep
data class DialogState(
    var isShowDelete: MutableState<Boolean>,
    var isShowDialog: MutableState<Boolean>,
    var isShowNewTranslateDialog: MutableState<Boolean>,
    var isShowReSummarize: MutableState<Boolean>,
    var isShowReTranscribe: MutableState<Boolean>,
    var isShowSelectLanguage: MutableState<Boolean>,
    var isShowSpeedMenu: MutableState<Boolean>,
    var showMenu: MutableState<Boolean>,
    var showShareMenu: MutableState<Boolean>,
    var showShareLinkMenu: MutableState<Boolean>,
    var showFixSpeakerName: MutableState<AudioTranscriptionViewModel.Speaker>,
    var showQuickFixAllDialog: MutableState<Boolean>,
    var showGuideDialog: MutableState<Boolean>
)

@Keep
data class TranscribedRequestContent(
    @SerializedName("speaker_name") val speakerName: String,
    @SerializedName("speaker_color") val speakerColor: String,
    @SerializedName("speak_content") val speakContent: String
)

@Keep
data class SummaryRequestContent(
    val title: String,
    val tag: String,
    val duration: String,
    val date: String,
    val speakerFocusContent: String? = null,
    val content: String
)

// 默认样式配置
object SpeakerTextDefaults {

    // 定义颜色数组
    private val speakerColors = arrayOf(
        Color1FA6AA,
        ColorD26913,
        Color129A37,
        Color2971CF,
        ColorCB2E34
    )

    private val speakNameColorMap = mutableMapOf<String, Color>()
    private val colorIndex = AtomicInteger(0)

    // 根据说话人编号获取颜色
    fun setSpeakerColor(speakerName: String) {
        // 计算索引，使用取模运算实现颜色循环
        if (speakNameColorMap[speakerName] != null) {
            return
        }
        val curIndex = colorIndex.get()
        if (curIndex >= speakerColors.size) {
            colorIndex.set(0)
        }
        val index = colorIndex.get() % speakerColors.size
        val newColor = speakerColors[index]
        speakNameColorMap[speakerName] = newColor
        colorIndex.incrementAndGet()
    }

    fun reset() {
        speakNameColorMap.clear()
        colorIndex.set(0)
    }

    fun getColor(speakerName: String): Color {
        return if (speakNameColorMap[speakerName] != null) {
            speakNameColorMap[speakerName]!!
        } else {
            val curIndex = colorIndex.get()
            if (curIndex >= speakerColors.size) {
                colorIndex.set(0)
            }
            speakerColors[colorIndex.get()]
        }
    }
}
