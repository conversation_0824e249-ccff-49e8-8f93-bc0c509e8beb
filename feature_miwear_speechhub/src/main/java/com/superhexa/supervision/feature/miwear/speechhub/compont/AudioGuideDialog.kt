package com.superhexa.supervision.feature.miwear.speechhub.compont

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Card
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.superhexa.supervision.feature.miwear.speechhub.R
import com.superhexa.supervision.library.base.basecommon.compose.BottomSheet
import com.superhexa.supervision.library.base.basecommon.compose.SubmitButton
import com.superhexa.supervision.library.base.basecommon.theme.*
import com.superhexa.supervision.library.base.data.model.ButtonParams

/**
 * 音频转录使用引导弹窗
 * 创建日期: 2025/7/31
 * 作者: Assistant
 */

data class GuideItem(
    val imageRes: Int,
    val titleRes: Int
)

@Composable
fun AudioGuideDialog(
    visible: Boolean,
    onDismiss: () -> Unit
) {
    val guideItems = listOf(
        GuideItem(
            imageRes = R.mipmap.audio_new_guide_01,
            titleRes = R.string.audio_guide_title_01
        ),
        GuideItem(
            imageRes = R.drawable.audio_new_guide_02,
            titleRes = R.string.audio_guide_title_02
        ),
        GuideItem(
            imageRes = R.drawable.audio_new_guide_03,
            titleRes = R.string.audio_guide_title_03
        ),
        GuideItem(
            imageRes = R.drawable.audio_new_guide_04,
            titleRes = R.string.audio_guide_title_04
        )
    )

    BottomSheet(
        visible = visible,
        onDismiss = onDismiss
    ) {
        Card(
            shape = RoundedCornerShape(topStart = Dp_16, topEnd = Dp_16),
            backgroundColor = Color18191A,
            elevation = Dp_0,
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(max = Dp_600)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = Dp_20),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 顶部间距
                Spacer(modifier = Modifier.height(Dp_24))
                
                // 标题
                Text(
                    text = stringResource(R.string.audio_guide_dialog_title),
                    fontSize = Sp_18,
                    fontWeight = FontWeight.W600,
                    color = ColorWhite,
                    textAlign = TextAlign.Center
                )
                
                Spacer(modifier = Modifier.height(Dp_20))
                
                // 引导内容列表
                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f),
                    verticalArrangement = Arrangement.spacedBy(Dp_16)
                ) {
                    itemsIndexed(guideItems) { index, item ->
                        GuideItemView(
                            item = item,
                            isLast = index == guideItems.size - 1
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(Dp_20))
                
                // 底部按钮
                SubmitButton(
                    subTitle = stringResource(R.string.audio_guide_got_it),
                    enable = true,
                    textColor = ColorBlack,
                    enableColors = listOf(Color26EAD9, Color17CBFF),
                    disableColors = listOf(Color26EAD9_30, Color17CBFF_30),
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(Dp_50)
                ) {
                    onDismiss()
                }
                
                Spacer(modifier = Modifier.height(Dp_30))
            }
        }
    }
}

@Composable
private fun GuideItemView(
    item: GuideItem,
    isLast: Boolean
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 描述文字
        Text(
            text = stringResource(item.titleRes),
            fontSize = Sp_14,
            fontWeight = FontWeight.W400,
            color = ColorWhite80,
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth()
        )
        
        Spacer(modifier = Modifier.height(Dp_12))
        
        // 引导图片
        Image(
            painter = painterResource(id = item.imageRes),
            contentDescription = null,
            contentScale = ContentScale.FillWidth,
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(max = Dp_120)
        )
        
        // 如果不是最后一项，添加底部间距
        if (!isLast) {
            Spacer(modifier = Modifier.height(Dp_16))
        }
    }
}
